#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全局日志配置模块
使用 rich 库实现美观的日志输出
"""

import logging
import sys
from pathlib import Path
from typing import Optional

from rich.console import Console
from rich.logging import RichHandler
from rich.traceback import install

# 安装 rich 的异常处理器，使异常信息更美观
install(show_locals=True)

# 创建控制台对象
console = Console(width=None)


class LoggerConfig:
    """
    全局日志配置类
    提供统一的日志配置和管理功能
    """
    
    _instance: Optional['LoggerConfig'] = None
    _initialized: bool = False
    
    def __new__(cls) -> 'LoggerConfig':
        """单例模式实现"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化日志配置"""
        if not self._initialized:
            self._setup_logging()
            LoggerConfig._initialized = True
    
    def _setup_logging(self) -> None:
        """
        设置日志配置
        配置 rich 处理器和文件处理器
        """
        # 创建日志目录
        log_dir = Path(__file__).parent.parent / "logs"
        log_dir.mkdir(exist_ok=True)
        
        # 配置根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)
        
        # 清除现有处理器
        root_logger.handlers.clear()
        
        # 创建 Rich 控制台处理器
        rich_handler = RichHandler(
            console=console,
            show_time=True,
            show_level=True,
            show_path=True,
            markup=True,
            rich_tracebacks=True,
            tracebacks_show_locals=True
        )
        rich_handler.setLevel(logging.INFO)
        
        # 创建文件处理器
        file_handler = logging.FileHandler(
            log_dir / "app.log",
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        
        # 设置日志格式
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)
        
        # 添加处理器到根日志器
        root_logger.addHandler(rich_handler)
        root_logger.addHandler(file_handler)
    
    @staticmethod
    def get_logger(name: str = None) -> logging.Logger:
        """
        获取日志器实例
        
        Args:
            name: 日志器名称，默认为调用模块的名称
            
        Returns:
            配置好的日志器实例
        """
        # 确保日志配置已初始化
        LoggerConfig()
        
        if name is None:
            # 获取调用者的模块名
            frame = sys._getframe(1)
            name = frame.f_globals.get('__name__', 'unknown')
        
        return logging.getLogger(name)
    
    @staticmethod
    def set_level(level: str) -> None:
        """
        设置全局日志级别
        
        Args:
            level: 日志级别 ('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')
        """
        numeric_level = getattr(logging, level.upper(), None)
        if not isinstance(numeric_level, int):
            raise ValueError(f'Invalid log level: {level}')
        
        logging.getLogger().setLevel(numeric_level)
        console.print(f"[bold green]日志级别已设置为: {level.upper()}[/bold green]")
    
    @staticmethod
    def print_success(message: str) -> None:
        """打印成功消息"""
        console.print(f"[bold green]✓ {message}[/bold green]")
    
    @staticmethod
    def print_error(message: str) -> None:
        """打印错误消息"""
        console.print(f"[bold red]✗ {message}[/bold red]")
    
    @staticmethod
    def print_warning(message: str) -> None:
        """打印警告消息"""
        console.print(f"[bold yellow]⚠ {message}[/bold yellow]")
    
    @staticmethod
    def print_info(message: str) -> None:
        """打印信息消息"""
        console.print(f"[bold blue]ℹ {message}[/bold blue]")


# 便捷函数
def get_logger(name: str = None) -> logging.Logger:
    """
    获取日志器的便捷函数
    
    Args:
        name: 日志器名称
        
    Returns:
        配置好的日志器实例
    """
    return LoggerConfig.get_logger(name)


# 导出的全局日志器实例
logger = get_logger(__name__)


if __name__ == "__main__":
    # 测试日志配置
    test_logger = get_logger("test")
    
    test_logger.debug("这是一条调试信息")
    test_logger.info("这是一条信息")
    test_logger.warning("这是一条警告")
    test_logger.error("这是一条错误")
    test_logger.critical("这是一条严重错误")
    
    # 测试便捷打印函数
    LoggerConfig.print_success("操作成功完成")
    LoggerConfig.print_error("发生了错误")
    LoggerConfig.print_warning("这是一个警告")
    LoggerConfig.print_info("这是一些信息")
    
    try:
        raise ValueError("这是一个测试异常")
    except Exception as e:
        test_logger.exception("捕获到异常")