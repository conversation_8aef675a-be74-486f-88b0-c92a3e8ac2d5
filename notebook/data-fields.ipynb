#%% md
# 获取 data fields

## 获取指定条件的 `数据字段`列表，单次最多 50 条记录
https://api.worldquantbrain.com/data-fields?&instrumentType=EQUITY&region=USA&delay=1&universe=TOP3000&dataset.id=analyst4&limit=50&offset={x}

## 单个`数据字段`网页
https://platform.worldquantbrain.com/data/data-sets/analyst4?delay=1&instrumentType=EQUITY&limit=50&offset=0&region=USA&universe=TOP3000
#%%
# 配置区域
dataset_id = 'analyst4'
step1_tag = "analyst4_usa_1step"
#%%
import pandas as pd

def get_datafields(
        s,
        instrument_type: str = 'EQUITY',
        region: str = 'USA',
        delay: int = 1,
        universe: str = 'TOP3000',
        dataset_id: str = '',
        search: str = ''
):
    if len(search) == 0:
        url_template = "https://api.worldquantbrain.com/data-fields?" + \
                       f"&instrumentType={instrument_type}" + \
                       f"&region={region}&delay={str(delay)}&universe={universe}&dataset.id={dataset_id}&limit=50" + \
                       "&offset={x}"
        print(f'url_template is ${url_template}')
        data_fields = s.get(url_template.format(x=0))
        # print(f'data_fields is ${data_fields}')
        data_fields_json = data_fields.json()
        # print(f'data_fields_json is ${data_fields_json}')
        count = data_fields.json()['count']
        print(f'count is ${count}')

    else:
        url_template = "https://api.worldquantbrain.com/data-fields?" + \
                       f"&instrumentType={instrument_type}" + \
                       f"&region={region}&delay={str(delay)}&universe={universe}&limit=50" + \
                       f"&search={search}" + \
                       "&offset={x}"
        count = 100

    # 分页循环，加载所有的 数据字段
    datafields_list = []
    for x in range(0, count, 50):
        datafields = s.get(url_template.format(x=x))
        datafields_list.append(datafields.json()['results'])

    # 变成二维表格
    datafields_list_flat = [
        item for sublist in datafields_list for item in sublist]

    datafields_df = pd.DataFrame(datafields_list_flat)
    return datafields_df
    
# digging_1step.py  L82
df = get_datafields(session, dataset_id=dataset_id,
                        region='USA', universe='TOP3000', delay=1)

print(df)
#%% md
# 这一步在做什么？
`process_datafields` 函数的作用是对原始数据字段进行预处理，为后续的因子生成做准备。具体步骤如下：
字段分类筛选：
如果 data_type 是 "matrix"，提取所有矩阵类型的字段ID
如果 data_type 是 "vector"，提取向量类型的字段ID并通过 get_vec_fields 进一步处理
数据预处理：
对每个字段应用两个关键的数据清洗操作
生成格式化的字段表达式供后续使用

## ts_backfill 操作

ts_backfill（时间序列回填） 是处理缺失数据的方法：
 - 作用：向前填充缺失的时间序列数据
        - 参数 120：表示最多向前回填120个时间点的数据
 - 目的：处理数据中的空值或缺失值
        - 确保时间序列的连续性
        - 为因子计算提供完整的数据基础

## winsorize 操作
winsorize（缩尾处理） 是一种统计学中的异常值处理方法：
 - 作用：限制极端异常值的影响，将超出一定范围的数值"裁剪"到边界值
        - 参数 std=4：表示将超出均值 ±4 个标准差的数值截断到边界值
 - 目的：
        - 防止极端异常值对模型造成过大影响
        - 提高数据的稳定性和可靠性
        - 减少噪声对因子质量的干扰

fields.py L380
### 对衍生资产公允价值进行缩尾处理
"winsorize (fn_derivative_fair_value_of_derivative_asset_a, std=3)",
#%%
# 常量
vec_ops = ["vec_avg", "vec_sum", "vec_ir", "vec_max",
           "vec_count", "vec_skewness", "vec_stddev", "vec_choose"]

def process_datafields(df, data_type):
    if data_type == "matrix":
        datafields = df[df['type'] == "MATRIX"]["id"].tolist()
    elif data_type == "vector":
        datafields = get_vec_fields(df[df['type'] == "VECTOR"]["id"].tolist())

    tb_fields = []
    for field in datafields:
        tb_fields.append("winsorize(ts_backfill(%s, 120), std=4)" % field)
    return tb_fields

def get_vec_fields(fields):
    vec_fields = []

    for field in fields:
        for vec_op in vec_ops:
            if vec_op == "vec_choose":
                vec_fields.append("%s(%s, nth=-1)" % (vec_op, field))
                vec_fields.append("%s(%s, nth=0)" % (vec_op, field))
            else:
                vec_fields.append("%s(%s)" % (vec_op, field))

    return (vec_fields)

# digging_1step.py  L84
pc_fields = process_datafields(
        df, "matrix") + process_datafields(df, "vector")
print(f'pc_fields is {pc_fields}')
#%%
